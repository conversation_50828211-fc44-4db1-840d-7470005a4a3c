error id: file://<WORKSPACE>/src/main/scala/StudentScoreManagementApp.scala:`<none>`.
file://<WORKSPACE>/src/main/scala/StudentScoreManagementApp.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -score.
	 -score#
	 -score().
	 -scala/Predef.score.
	 -scala/Predef.score#
	 -scala/Predef.score().
offset: 5495
uri: file://<WORKSPACE>/src/main/scala/StudentScoreManagementApp.scala
text:
```scala
import scala.io.StdIn
import scala.util.{Try, Success, Failure}

object StudentScoreManagementApp {
  def main(args: Array[String]): Unit = {
    println("=" * 60)
    println("欢迎使用学生成绩管理系统！")
    println("=" * 60)
    
    val csvPath = "src/main/resources/student_scores/student-scores.csv"
    val manager = new StudentScoreManager(csvPath)
    
    // 加载数据
    manager.loadData()
    
    var running = true
    while (running) {
      showMenu()
      val choice = StdIn.readLine("请选择操作 (1-9): ").trim
      
      choice match {
        case "1" => 
          addStudent(manager)
          waitForUserInput()
        case "2" => 
          deleteStudent(manager)
          waitForUserInput()
        case "3" => 
          updateStudent(manager)
          waitForUserInput()
        case "4" => 
          updateStudentScore(manager)
          waitForUserInput()
        case "5" => 
          queryStudent(manager)
          waitForUserInput()
        case "6" => 
          listAllStudents(manager)
          waitForUserInput()
        case "7" => 
          queryByScoreRange(manager)
          waitForUserInput()
        case "8" => 
          showStatistics(manager)
          waitForUserInput()
        case "9" => 
          manager.saveData()
          println("感谢使用学生成绩管理系统！再见！")
          running = false
        case _ => 
          println("无效选择，请重新输入")
          waitForUserInput()
      }
      
      if (running) {
        println("\n" + "=" * 60 + "\n")
      }
    }
  }

  def showMenu(): Unit = {
    println("学生成绩管理系统菜单:")
    println("1. 添加学生")
    println("2. 删除学生")
    println("3. 修改学生信息")
    println("4. 修改学生成绩")
    println("5. 查询学生")
    println("6. 显示所有学生")
    println("7. 按成绩范围查询")
    println("8. 显示统计信息")
    println("9. 保存并退出")
    println("-" * 40)
  }

  def addStudent(manager: StudentScoreManager): Unit = {
    println("添加新学生:")
    
    try {
      val id = Try(StdIn.readLine("学生ID (留空自动生成): ").trim) match {
        case Success(input) if input.nonEmpty => input.toInt
        case _ => manager.getNextAvailableId
      }
      
      val firstName = StdIn.readLine("名: ").trim
      val lastName = StdIn.readLine("姓: ").trim
      val email = StdIn.readLine("邮箱: ").trim
      val gender = StdIn.readLine("性别 (male/female): ").trim
      
      val partTimeJob = StdIn.readLine("是否有兼职工作 (true/false): ").trim.toLowerCase == "true"
      val absenceDays = StdIn.readLine("缺席天数: ").trim.toInt
      val extracurricular = StdIn.readLine("是否参与课外活动 (true/false): ").trim.toLowerCase == "true"
      val studyHours = StdIn.readLine("每周自习时间: ").trim.toInt
      val career = StdIn.readLine("职业抱负: ").trim
      
      println("请输入各科成绩:")
      val mathScore = StdIn.readLine("数学成绩: ").trim.toDouble
      val historyScore = StdIn.readLine("历史成绩: ").trim.toDouble
      val physicsScore = StdIn.readLine("物理成绩: ").trim.toDouble
      val chemistryScore = StdIn.readLine("化学成绩: ").trim.toDouble
      val biologyScore = StdIn.readLine("生物成绩: ").trim.toDouble
      val englishScore = StdIn.readLine("英语成绩: ").trim.toDouble
      val geographyScore = StdIn.readLine("地理成绩: ").trim.toDouble
      
      val student = Student(
        id, firstName, lastName, email, gender, partTimeJob,
        absenceDays, extracurricular, studyHours, career,
        mathScore, historyScore, physicsScore, chemistryScore,
        biologyScore, englishScore, geographyScore
      )
      
      manager.addStudent(student)
      
    } catch {
      case e: NumberFormatException => println("输入格式错误，请输入有效的数字")
      case e: Exception => println(s"添加学生时出错: ${e.getMessage}")
    }
  }

  def deleteStudent(manager: StudentScoreManager): Unit = {
    println("删除学生:")
    try {
      val id = StdIn.readLine("请输入要删除的学生ID: ").trim.toInt
      manager.deleteStudent(id)
    } catch {
      case e: NumberFormatException => println("请输入有效的数字ID")
      case e: Exception => println(s"删除学生时出错: ${e.getMessage}")
    }
  }

  def updateStudent(manager: StudentScoreManager): Unit = {
    println("修改学生信息:")
    try {
      val id = StdIn.readLine("请输入要修改的学生ID: ").trim.toInt
      
      manager.findStudentById(id) match {
        case Some(student) =>
          println(s"当前学生信息: $student")
          println("请输入新的信息 (留空保持原值):")
          
          val firstName = getInputOrDefault("名", student.firstName)
          val lastName = getInputOrDefault("姓", student.lastName)
          val email = getInputOrDefault("邮箱", student.email)
          val gender = getInputOrDefault("性别", student.gender)
          val career = getInputOrDefault("职业抱负", student.careerAspiration)
          
          val updatedStudent = student.copy(
            firstName = firstName,
            lastName = lastName,
            email = email,
            gender = gender,
            careerAspiration = career
          )
          
          manager.updateStudent(id, updatedStudent)
          
        case None =>
          println(s"未找到ID为 $id 的学生")
      }
    } catch {
      case e: NumberFormatException => println("请输入有效的数字ID")
      case e: Exception => println(s"修改学生信息时出错: ${e.getMessage}")
    }
  }

  def updateStudentScore(manager: StudentScoreManager): Unit = {
    println("修改学生成绩:")
    try {
      val id = StdIn.readLine("请输入学生ID: ").trim.toInt
      val subject = StdIn.readLine("请输入科目 (math/history/physics/chemistry/biology/english/geography): ").trim
      val score = StdIn.readLine("请输入新成绩: ").trim.toDouble
      
      manager.updateStudentScores(id, subject, score@@)
    } catch {
      case e: NumberFormatException => println("请输入有效的数字")
      case e: Exception => println(s"修改成绩时出错: ${e.getMessage}")
    }
  }

  def queryStudent(manager: StudentScoreManager): Unit = {
    println("查询学生:")
    println("1. 按ID查询")
    println("2. 按姓名查询")
    
    val choice = StdIn.readLine("请选择查询方式 (1-2): ").trim
    
    choice match {
      case "1" =>
        try {
          val id = StdIn.readLine("请输入学生ID: ").trim.toInt
          manager.findStudentById(id) match {
            case Some(student) => println(s"找到学生: $student")
            case None => println(s"未找到ID为 $id 的学生")
          }
        } catch {
          case e: NumberFormatException => println("请输入有效的数字ID")
        }
        
      case "2" =>
        val name = StdIn.readLine("请输入学生姓名 (支持部分匹配): ").trim
        val students = manager.findStudentByName(name)
        if (students.nonEmpty) {
          println(s"找到 ${students.length} 个匹配的学生:")
          students.foreach(println)
        } else {
          println("未找到匹配的学生")
        }
        
      case _ => println("无效选择")
    }
  }

  def listAllStudents(manager: StudentScoreManager): Unit = {
    val students = manager.getAllStudents
    if (students.nonEmpty) {
      println(s"共有 ${students.length} 名学生:")
      println("-" * 80)
      students.foreach(println)
    } else {
      println("没有学生记录")
    }
  }

  def queryByScoreRange(manager: StudentScoreManager): Unit = {
    println("按成绩范围查询学生:")
    try {
      val subject = StdIn.readLine("请输入科目 (math/history/physics/chemistry/biology/english/geography/average): ").trim
      val minScore = StdIn.readLine("请输入最低分数: ").trim.toDouble
      val maxScore = StdIn.readLine("请输入最高分数: ").trim.toDouble
      
      val students = manager.findStudentsByScoreRange(subject, minScore, maxScore)
      if (students.nonEmpty) {
        println(s"找到 ${students.length} 名学生在 $subject 科目成绩在 $minScore-$maxScore 之间:")
        println("-" * 80)
        students.foreach(println)
      } else {
        println("未找到符合条件的学生")
      }
    } catch {
      case e: NumberFormatException => println("请输入有效的数字")
      case e: Exception => println(s"查询时出错: ${e.getMessage}")
    }
  }

  def showStatistics(manager: StudentScoreManager): Unit = {
    println("成绩统计信息:")
    val subjects = List("math", "history", "physics", "chemistry", "biology", "english", "geography", "average")
    val subjectNames = List("数学", "历史", "物理", "化学", "生物", "英语", "地理", "平均分")
    
    subjects.zip(subjectNames).foreach { case (subject, name) =>
      manager.getScoreStatistics(subject) match {
        case Some((min, max, avg)) =>
          println(f"$name%-6s: 最低分 $min%5.1f, 最高分 $max%5.1f, 平均分 $avg%5.2f")
        case None =>
          println(s"$name: 无数据")
      }
    }
  }

  def getInputOrDefault(prompt: String, defaultValue: String): String = {
    val input = StdIn.readLine(s"$prompt (当前: $defaultValue): ").trim
    if (input.nonEmpty) input else defaultValue
  }

  def waitForUserInput(): Unit = {
    println("\n按回车键继续...")
    StdIn.readLine()
  }
} 
```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.