error id: file://<WORKSPACE>/src/main/scala/ApacheLogAnalyzerRDD.scala:`<none>`.
file://<WORKSPACE>/src/main/scala/ApacheLogAnalyzerRDD.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:

offset: 830
uri: file://<WORKSPACE>/src/main/scala/ApacheLogAnalyzerRDD.scala
text:
```scala
import org.apache.spark.{SparkConf, SparkContext}
import org.apache.spark.rdd.RDD
import java.text.SimpleDateFormat
import java.util.{Date, Locale}
import scala.util.{Try, Success, Failure}

/**
 * Web日志分析系统 - 使用RDD API实现
 * 功能：解析Apache日志文件并分析异常状态码
 */
object ApacheLogAnalyzerRDD {
  
  // 定义日志记录的case class
  case class LogRecord(
    ip: String,
    timestamp: Date,
    method: String,
    url: String,
    protocol: String,
    statusCode: Int,
    dataSize: Long,
    referer: String,
    userAgent: String,
    day: String
  )
  
  // 异常状态码定义（4xx和5xx）
  private def isErrorStatus(statusCode: Int): Boolean = {
    statusCode >= 400
  }
  
  /**
   * 解析单行Apache日志
   * 使用函数式编程风格，返回Option[LogRecord]
   */
  private def parseLogLine(line: String): Option[LogRecord] = {
    // Apache Combined Log Format 正则表达式（包含Referer和User-@@Agent）
    val logPattern = """^(\S+) \S+ \S+ \[([\w:/]+\s[+\-]\d{4})\] "(\S+) (\S+) (\S+)" (\d{3}) (\d+) "([^"]*)" "([^"]*)".*""".r
    
    logPattern.findFirstMatchIn(line).flatMap { m =>
      Try {
        val ip = m.group(1)
        val timestampStr = m.group(2)
        val method = m.group(3)
        val url = m.group(4)
        val protocol = m.group(5)
        val statusCode = m.group(6).toInt
        val dataSize = m.group(7).toLong
        val referer = m.group(8)
        val userAgent = m.group(9)
        
        // 解析时间戳
        val dateFormat = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss Z", Locale.ENGLISH)
        val timestamp = dateFormat.parse(timestampStr)
        
        // 生成日期字符串
        val dayFormat = new SimpleDateFormat("yyyy-MM-dd")
        val day = dayFormat.format(timestamp)
        
        LogRecord(ip, timestamp, method, url, protocol, statusCode, dataSize, referer, userAgent, day)
      }.toOption
    }
  }
  
  /**
   * 解析日志文件并返回LogRecord的RDD
   */
  private def parseLogFile(sc: SparkContext, logFilePath: String): RDD[LogRecord] = {
    sc.textFile(logFilePath)
      .map(parseLogLine)
      .filter(_.isDefined)
      .map(_.get)
  }
  
  /**
   * 分析最常见的异常状态码
   */
  private def analyzeErrorStatusCodes(logRecords: RDD[LogRecord], topN: Int = 3): List[(Int, Long)] = {
    val errorStatusCounts = logRecords
      .filter(record => isErrorStatus(record.statusCode))  // 过滤异常状态码
      .map(record => (record.statusCode, 1L))              // 转换为(状态码, 1)的键值对
      .reduceByKey(_ + _)                                   // 按状态码聚合计数
      .collect()                                            // 收集到Driver端
    
    // 在Driver端进行排序，避免分布式排序的序列化问题
    errorStatusCounts
      .sortBy(-_._2)                                        // 按计数降序排序
      .take(topN)                                           // 取前N个
      .toList
  }
  
  /**
   * 格式化输出异常状态码分析结果
   */
  private def printErrorStatusAnalysis(errorStatusCodes: List[(Int, Long)]): Unit = {
    println("=== Web日志分析系统 - 基于RDD API ===\n")
    println("最常见的3个异常状态码分析结果：")
    println("-" * 50)
    
    if (errorStatusCodes.isEmpty) {
      println("未发现异常状态码（4xx或5xx）")
    } else {
      errorStatusCodes.zipWithIndex.foreach { case ((statusCode, count), index) =>
        val statusDescription = statusCode match {
          case 400 => "Bad Request (错误请求)"
          case 401 => "Unauthorized (未授权)"
          case 403 => "Forbidden (禁止访问)"
          case 404 => "Not Found (页面未找到)"
          case 500 => "Internal Server Error (服务器内部错误)"
          case 502 => "Bad Gateway (错误网关)"
          case 503 => "Service Unavailable (服务不可用)"
          case _   => "其他错误"
        }
        
        println(f"${index + 1}. 状态码 $statusCode ($statusDescription): $count%,d 次")
      }
    }
    println("-" * 50)
  }
  
  /**
   * 额外的统计信息展示
   */
  private def printAdditionalStats(logRecords: RDD[LogRecord]): Unit = {
    val totalRecords = logRecords.count()
    val uniqueIPs = logRecords.map(_.ip).distinct().count()
    val uniqueURLs = logRecords.map(_.url).distinct().count()
    val uniqueDays = logRecords.map(_.day).distinct().count()
    val errorRecords = logRecords.filter(record => isErrorStatus(record.statusCode)).count()
    val errorRate = if (totalRecords > 0) (errorRecords.toDouble / totalRecords) * 100 else 0.0
    
    println("\n=== 详细统计信息 ===")
    println(f"总日志记录数: $totalRecords%,d")
    println(f"唯一IP地址数: $uniqueIPs%,d") 
    println(f"唯一URL数量: $uniqueURLs%,d")
    println(f"数据覆盖天数: $uniqueDays%,d 天")
    println(f"异常请求数量: $errorRecords%,d")
    println(f"异常率: $errorRate%.2f%%")
    
    // 显示请求方法分布
    println(f"\n请求方法分布（前5名）:")
    val methodStats = logRecords
      .map(record => (record.method, 1L))
      .reduceByKey(_ + _)
      .collect()
      .sortBy(-_._2)
      .take(5)
    
    methodStats.foreach { case (method, count) =>
      println(f"  $method: $count%,d 次")
    }
    
    // 显示状态码分布
    println(f"\n状态码分布（前5名）:")
    val statusStats = logRecords
      .map(record => (record.statusCode, 1L))
      .reduceByKey(_ + _)
      .collect()
      .sortBy(-_._2)
      .take(5)
    
    statusStats.foreach { case (status, count) =>
      val statusDesc = status match {
        case 200 => "OK (成功)"
        case 404 => "Not Found (页面未找到)"
        case 403 => "Forbidden (禁止访问)"
        case 500 => "Internal Server Error (服务器错误)"
        case 301 => "Moved Permanently (永久重定向)"
        case 302 => "Found (临时重定向)"
        case _ => "其他"
      }
      println(f"  $status ($statusDesc): $count%,d 次")
    }
  }
  
  def main(args: Array[String]): Unit = {
    // 配置Spark
    val conf = new SparkConf()
      .setAppName("Web Log Analyzer with RDD")
      .setMaster("local[*]")
      .set("spark.serializer", "org.apache.spark.serializer.JavaSerializer")  // 使用Java序列化器避免Kryo问题
      .set("spark.sql.adaptive.enabled", "false")  // 禁用自适应查询执行
      
    val sc = new SparkContext(conf)
    
    try {
      // 日志文件路径
      val logFilePath = "src/main/resources/apache_logs.txt"
      
      println("开始解析Apache日志文件...")
      
      // 1. 读取并解析日志文件
      val logRecords = parseLogFile(sc, logFilePath)
      
      // 缓存RDD以提高后续操作性能
      logRecords.cache()
      
      println(s"成功解析日志文件，共处理 ${logRecords.count()} 条有效记录\n")
      
      // 2. 分析最常见的3个异常状态码
      val topErrorStatusCodes = analyzeErrorStatusCodes(logRecords, 3)
      
      // 3. 输出结果
      printErrorStatusAnalysis(topErrorStatusCodes)
      
      // 4. 输出额外统计信息
      printAdditionalStats(logRecords)
      
      // 释放缓存
      logRecords.unpersist()
      
    } catch {
      case e: Exception =>
        println(s"分析过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
      println("\nSpark上下文已关闭，程序执行完毕。")
    }
  }
} 
```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.