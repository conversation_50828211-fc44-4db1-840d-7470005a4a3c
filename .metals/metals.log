2025.05.27 10:21:45 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
2025.05.27 10:21:45 INFO  Shutting down server
2025.05.27 10:21:45 INFO  shutting down Metals
2025.05.27 10:21:48 INFO  Attempting to connect to the build server...
2025.05.27 10:21:48 INFO  Found a Bloop server running
2025.05.27 10:21:48 INFO  Exiting server
2025.05.27 18:29:28 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
2025.05.27 18:29:29 INFO  Attempting to connect to the build server...
2025.05.27 18:29:29 INFO  Found a Bloop server running
2025.05.27 18:29:29 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:29:29 INFO  time: Connected to build server in 0.26s
2025.05.27 18:29:29 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:29:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:29:29 INFO  time: Imported build in 0.16s
2025.05.27 18:29:33 INFO  Shutting down server
2025.05.27 18:29:33 INFO  shutting down Metals
2025.05.27 18:29:33 INFO  Shut down connection with build server.
2025.05.27 18:29:33 INFO  Exiting server
2025.05.27 18:29:35 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
5月 27, 2025 6:29:36 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
5月 27, 2025 6:29:36 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2025.05.27 18:29:36 INFO  Attempting to connect to the build server...
2025.05.27 18:29:36 INFO  Found a Bloop server running
2025.05.27 18:29:36 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:29:36 INFO  time: Connected to build server in 0.19s
2025.05.27 18:29:36 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:29:36 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:36 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:36 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:36 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:36 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:29:37 INFO  time: indexed workspace in 1.26s
2025.05.27 18:29:45 INFO  Shutting down server
2025.05.27 18:29:45 INFO  shutting down Metals
2025.05.27 18:29:45 INFO  Shut down connection with build server.
2025.05.27 18:29:45 INFO  Exiting server
2025.05.27 18:29:47 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
5月 27, 2025 6:29:48 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2025.05.27 18:29:48 INFO  Attempting to connect to the build server...
2025.05.27 18:29:48 INFO  Found a Bloop server running
2025.05.27 18:29:49 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:29:49 INFO  time: Connected to build server in 0.2s
2025.05.27 18:29:49 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:29:49 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:49 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:49 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:49 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:29:49 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:29:50 INFO  time: indexed workspace in 1.27s
2025.05.27 18:30:00 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:30:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
5月 27, 2025 6:30:11 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2025.05.27 18:30:11 INFO  Shutting down server
2025.05.27 18:30:11 INFO  shutting down Metals
2025.05.27 18:30:11 INFO  Shut down connection with build server.
2025.05.27 18:30:14 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
5月 27, 2025 6:30:15 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2025.05.27 18:30:15 INFO  Attempting to connect to the build server...
2025.05.27 18:30:15 INFO  Found a Bloop server running
2025.05.27 18:30:16 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:30:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:30:16 INFO  time: Connected to build server in 0.56s
2025.05.27 18:30:16 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:30:16 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:30:16 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:30:16 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:30:16 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:30:16 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:30:16 INFO  time: Imported build in 0.25s
2025.05.27 18:30:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
java.util.concurrent.RejectedExecutionException: Task Future(<not completed>) rejected from java.util.concurrent.ThreadPoolExecutor@2999a246[Shutting down, pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 256]
	at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2065)
	at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:833)
	at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1365)
	at scala.concurrent.impl.ExecutionContextImpl.execute(ExecutionContextImpl.scala:21)
	at scala.concurrent.impl.Promise$Transformation.submitWithValue(Promise.scala:429)
	at scala.concurrent.impl.Promise$DefaultPromise.submitWithValue(Promise.scala:338)
	at scala.concurrent.impl.Promise$DefaultPromise.tryComplete0(Promise.scala:285)
	at scala.concurrent.impl.Promise$Transformation.handleFailure(Promise.scala:444)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:506)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
5月 27, 2025 6:30:55 下午 org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleCancellation
警告: Unmatched cancel notification for request id 8
2025.05.27 18:30:56 INFO  time: indexed workspace in 1.19s
2025.05.27 18:30:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:30:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:30:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:32:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:32:11 INFO  Shutting down server
2025.05.27 18:32:11 INFO  shutting down Metals
2025.05.27 18:32:11 INFO  Shut down connection with build server.
2025.05.27 18:32:11 INFO  Exiting server
2025.05.27 18:32:14 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
5月 27, 2025 6:32:15 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2025.05.27 18:32:15 INFO  Attempting to connect to the build server...
2025.05.27 18:32:15 INFO  Found a Bloop server running
2025.05.27 18:32:15 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:32:15 INFO  time: Connected to build server in 0.17s
2025.05.27 18:32:15 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:32:15 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:32:15 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:32:15 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:32:15 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:32:15 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:32:15 INFO  time: Imported build in 0.11s
2025.05.27 18:32:16 INFO  time: indexed workspace in 1.26s
2025.05.27 18:32:16 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:32:16 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:32:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:32:22 INFO  time: code lens generation in 5.61s
2025.05.27 18:32:22 INFO  time: code lens generation in 5.31s
2025.05.27 18:32:22 INFO  time: code lens generation in 5.61s
2025.05.27 18:34:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:34:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:33 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:37:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:47 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:37:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:37:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:14 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:15 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:15 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:15 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:53 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:38:53 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:38:53 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:40:35 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:40:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:40:38 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:40:39 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:40:39 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:40:40 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:43:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:43:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:43:26 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:43:26 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:43:26 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:43:27 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:43:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:43:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:43:35 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:43:35 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:43:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:45:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:45:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:46:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
Exception in thread "pool-48-thread-1" java.lang.InterruptedException
	at scala.meta.internal.metals.FutureCancelToken.checkCanceled(FutureCancelToken.scala:29)
	at scala.meta.internal.pc.CompilerAccess.onCompilerJobQueue$$anonfun$1(CompilerAccess.scala:244)
	at scala.meta.internal.pc.CompilerJobQueue$Job.run(CompilerJobQueue.scala:153)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
5月 27, 2025 6:46:34 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: empty definition using pc, found symbol in pc: `<none>`. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/metals-full/2025-05-27/r_empty-definition_18-46-34-911.md)
2025.05.27 18:46:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:47:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:47:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:47:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:47:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:47:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:47:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:47:45 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:47:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:47:55 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:48:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:48:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:48:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala
2025.05.27 18:50:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManagementApp.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:50:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala
2025.05.27 18:50:50 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/StudentScoreManager.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:50:50 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala
2025.05.27 18:50:50 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/Student.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:51:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:51:04 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:51:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:31 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:33 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:34 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/test_regex.scala
2025.05.27 18:52:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/test_regex.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:34 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/test_regex.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/test_regex.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:44 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/test_regex.scala
2025.05.27 18:52:46 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:52:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:53:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala
2025.05.27 18:53:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:53:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:53:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:54:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:54:06 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:54:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:54:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/WebLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:54:54 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:55:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:20 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:55:25 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:25 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:25 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:55:25 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:55:28 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:28 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:55:28 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:29 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:55:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:28 INFO  Detected new build tool in /Users/<USER>/IdeaProjects/SparkFinal
2025.05.27 18:55:28 INFO  Disconnecting from Bloop session...
2025.05.27 18:55:29 INFO  Shut down connection with build server.
2025.05.27 18:55:29 INFO  Attempting to connect to the build server...
2025.05.27 18:55:29 INFO  Found a Bloop server running
2025.05.27 18:55:29 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.27 18:55:29 INFO  time: Connected to build server in 0.12s
2025.05.27 18:55:29 INFO  Connected to Build server: Bloop v2.0.10
5月 27, 2025 6:55:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:55:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:55:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:55:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 27, 2025 6:55:29 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.27 18:55:29 INFO  time: indexed workspace in 0.66s
2025.05.27 18:55:29 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:55:29 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:55:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:55:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:55:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:56:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:56:32 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:32 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:56:32 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:34 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:56:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:56:38 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:57:11 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:11 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:11 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:57:11 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:11 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:57:11 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:17 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:57:17 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:17 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:19 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:57:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:21 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:57:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:25 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:26 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 18:57:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:34 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:57:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:58 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:59 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:57:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:57:59 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 18:58:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:58:01 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:58:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:58:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:58:35 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 18:58:35 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:58:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 18:58:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:07:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
5月 27, 2025 7:08:00 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: empty definition using pc, found symbol in pc: scala/Boolean# (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/metals-full/2025-05-27/r_empty-definition_19-08-00-885.md)
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:15 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 19:08:15 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:15 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:20 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:08:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:34 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:36 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:45 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala
2025.05.27 19:08:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:50 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:50 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:50 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:08:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:52 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:52 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:52 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:08:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:08:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:08:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:04 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:09:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:06 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:09:06 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:08 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:08 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:09 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:10 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:09:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:10 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:09:15 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:21 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:09:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.05.27 19:09:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:30 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:09:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
5月 27, 2025 7:09:44 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: empty definition using pc, found symbol in pc: `<none>`. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/metals-full/2025-05-27/r_empty-definition_19-09-44-432.md)
2025.05.27 19:09:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:09:45 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:10:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:10:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:10:50 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:11:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:11:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:11:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:11:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:12:12 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:12:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:12:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:12:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:12:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.05.27 19:13:34 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:13:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:13:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:14:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:06 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.27 19:15:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:38 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:39 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:39 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:40 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:15:40 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:16:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
5月 27, 2025 7:16:07 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: empty definition using pc, found symbol in pc: `<none>`. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/metals-full/2025-05-27/r_empty-definition_19-09-44-432.md)
2025.05.27 19:16:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:16:08 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
5月 27, 2025 7:16:09 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: empty definition using pc, found symbol in pc: `<none>`. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/metals-full/2025-05-27/r_empty-definition_19-09-44-432.md)
2025.05.27 19:16:09 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:16:09 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:21:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 19:21:12 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 20:12:09 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.27 20:12:09 INFO  Shutting down server
2025.05.27 20:12:09 INFO  shutting down Metals
2025.05.27 20:12:09 INFO  Shut down connection with build server.
2025.05.27 20:12:09 INFO  Exiting server
2025.05.28 15:36:06 INFO  Started: Metals version 1.5.3 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
2025.05.28 15:36:07 INFO  Attempting to connect to the build server...
2025.05.28 15:36:07 INFO  Found a Bloop server running
2025.05.28 15:36:08 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.05.28 15:36:08 INFO  time: Connected to build server in 0.21s
2025.05.28 15:36:08 INFO  Connected to Build server: Bloop v2.0.10
5月 28, 2025 3:36:08 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 28, 2025 3:36:08 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 28, 2025 3:36:08 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 28, 2025 3:36:08 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
5月 28, 2025 3:36:08 下午 scala.meta.internal.metals.StdReporter $anonfun$create$1
警告: Empty build targets. Expected at least one build target identifier. (full report at: /Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md)
2025.05.28 15:36:10 INFO  time: indexed workspace in 2.82s
2025.05.28 15:36:11 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.28 15:36:11 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.05.28 15:36:10 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.28 15:36:12 INFO  time: code lens generation in 1.59s
2025.05.28 15:36:12 INFO  time: code lens generation in 1.3s
2025.05.28 15:36:12 INFO  time: code lens generation in 1.59s
2025.05.28 15:36:15 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.05.28 15:36:15 INFO  Shutting down server
2025.05.28 15:36:15 INFO  shutting down Metals
2025.05.28 15:36:15 INFO  Shut down connection with build server.
2025.05.28 15:36:15 INFO  Exiting server
2025.06.09 13:18:04 INFO  Started: Metals version 1.6.0 in folders '/Users/<USER>/IdeaProjects/SparkFinal' for client Cursor 1.96.2.
2025.06.09 13:18:05 INFO  Attempting to connect to the build server...
2025.06.09 13:18:05 INFO  Found a Bloop server running
2025.06.09 13:18:05 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.06.09 13:18:05 INFO  time: Connected to build server in 0.19s
2025.06.09 13:18:05 INFO  Connected to Build server: Bloop v2.0.10
2025.06.09 13:18:05 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:18:05 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:18:05 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:18:05 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:18:05 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:18:07 INFO  time: indexed workspace in 1.8s
2025.06.09 13:18:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.06.09 13:18:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.06.09 13:18:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:18:10 INFO  time: code lens generation in 2.91s
2025.06.09 13:18:10 INFO  time: code lens generation in 2.91s
2025.06.09 13:20:55 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala
2025.06.09 13:20:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/ApacheLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:23:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:23:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:23:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:18 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:18 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:20 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:19 INFO  Detected new build tool in /Users/<USER>/IdeaProjects/SparkFinal
2025.06.09 13:24:20 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:19 INFO  Disconnecting from Bloop session...
2025.06.09 13:24:20 INFO  Shut down connection with build server.
2025.06.09 13:24:20 INFO  Attempting to connect to the build server...
2025.06.09 13:24:20 INFO  Found a Bloop server running
2025.06.09 13:24:20 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:20 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.06.09 13:24:20 INFO  time: Connected to build server in 0.12s
2025.06.09 13:24:20 INFO  Connected to Build server: Bloop v2.0.10
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  time: indexed workspace in 0.71s
2025.06.09 13:24:20 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:20 INFO  Detected new build tool in /Users/<USER>/IdeaProjects/SparkFinal
2025.06.09 13:24:20 INFO  Disconnecting from Bloop session...
2025.06.09 13:24:20 INFO  Shut down connection with build server.
2025.06.09 13:24:20 INFO  Attempting to connect to the build server...
2025.06.09 13:24:20 INFO  Found a Bloop server running
2025.06.09 13:24:20 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.06.09 13:24:20 INFO  time: Connected to build server in 0.11s
2025.06.09 13:24:20 INFO  Connected to Build server: Bloop v2.0.10
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:20 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:24:22 INFO  time: indexed workspace in 0.7s
2025.06.09 13:24:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:24:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:25 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:25 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:24:25 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:07 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:08 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:08 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:08 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:09 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:09 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:09 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:25:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:25:46 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:25:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:25:48 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:26:28 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:30 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:30 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:30 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:32 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:32 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:32 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:33 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:36 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:37 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:37 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:44 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:44 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:47 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:47 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:47 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:48 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:54 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:54 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:28:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:28:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:28:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:00 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:29:22 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:29:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:55 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:29:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:29:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:29:58 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:29:58 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:29:58 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:01 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:30:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:02 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:02 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:04 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:30:07 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:23 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:23 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:30:30 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:43 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:55 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:30:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:30:59 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:30:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:30:59 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:21 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:31:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:31:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:54 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:55 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:56 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:58 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:31:58 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:16 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:32:16 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:19 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:19 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:32:19 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:32:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:21 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:32:21 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:32:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:38:14 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:38:14 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:39:28 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:39:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:40:43 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:40:43 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:40:43 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:40:56 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala
2025.06.09 13:41:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/NovelWritingModel.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:51:53 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:51:53 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:51:53 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:49 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:51 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:51 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:52 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:52:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:52:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:57 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:52:57 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:53:00 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:00 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:53:00 INFO  Detected new build tool in /Users/<USER>/IdeaProjects/SparkFinal
2025.06.09 13:53:01 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:53:01 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:00 INFO  Disconnecting from Bloop session...
2025.06.09 13:53:01 INFO  Shut down connection with build server.
2025.06.09 13:53:01 INFO  Attempting to connect to the build server...
2025.06.09 13:53:01 INFO  Found a Bloop server running
2025.06.09 13:53:01 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.06.09 13:53:01 INFO  time: Connected to build server in 0.12s
2025.06.09 13:53:01 INFO  Connected to Build server: Bloop v2.0.10
2025.06.09 13:53:01 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:01 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:01 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:01 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:01 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:01 INFO  time: indexed workspace in 0.68s
2025.06.09 13:53:01 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:53:01 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:02 INFO  Detected new build tool in /Users/<USER>/IdeaProjects/SparkFinal
2025.06.09 13:53:02 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:53:02 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:02 INFO  Disconnecting from Bloop session...
2025.06.09 13:53:02 INFO  Shut down connection with build server.
2025.06.09 13:53:02 INFO  Attempting to connect to the build server...
2025.06.09 13:53:02 INFO  Found a Bloop server running
2025.06.09 13:53:02 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /Users/<USER>/IdeaProjects/SparkFinal/.metals/bsp.trace.json or /Users/<USER>/Library/Caches/org.scalameta.metals/bsp.trace.json
2025.06.09 13:53:02 INFO  time: Connected to build server in 0.11s
2025.06.09 13:53:02 INFO  Connected to Build server: Bloop v2.0.10
2025.06.09 13:53:02 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:02 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:02 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:02 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:02 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/Users/<USER>/IdeaProjects/SparkFinal/.metals/.reports/bloop/2025-05-27/r_Empty build targets...._18-29-29-908.md")
2025.06.09 13:53:03 INFO  time: indexed workspace in 0.68s
2025.06.09 13:53:02 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:53:02 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/build.sbt
2025.06.09 13:53:27 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:53:38 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:53:38 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:03 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:03 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:04 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:05 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:05 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:54:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:42 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala
2025.06.09 13:54:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleLogAnalyzerRDD.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:45 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:45 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:54:53 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:54:53 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:55:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:55:24 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:55:24 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala
2025.06.09 13:55:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:55:42 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:55:49 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:57:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:57:06 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:57:06 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:57:13 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:57:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:57:13 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 13:57:28 WARN  no build target for: /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SimpleDecisionTreeAnalyzer.scala
2025.06.09 13:57:33 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/build.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 14:00:28 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.06.09 14:00:29 INFO  no build target found for /Users/<USER>/IdeaProjects/SparkFinal/src/main/scala/SparkDecisionTreeAnalyzer.scala. Using presentation compiler with project's scala-library version: 3.3.6
