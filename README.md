# 学生成绩管理系统 (Student Score Management System)

这是一个基于Scala编程语言开发的命令行学生成绩管理系统，可以对学生数据进行增加、删除、修改和查询操作。

## 功能特性

- **数据加载**: 自动从CSV文件加载学生数据
- **学生管理**: 添加、删除、修改学生信息
- **成绩管理**: 修改学生各科成绩
- **查询功能**: 
  - 按ID查询学生
  - 按姓名查询学生（支持部分匹配）
  - 按成绩范围查询学生
- **统计功能**: 显示各科成绩的最高分、最低分和平均分
- **数据持久化**: 自动保存数据到CSV文件

## 项目结构

```
src/main/scala/
├── Student.scala                    # 学生数据模型
├── StudentScoreManager.scala       # 学生成绩管理类
└── StudentScoreManagementApp.scala # 主应用程序

src/main/resources/student_scores/
└── student-scores.csv              # 学生数据文件
```

## 如何运行

1. 确保你已经安装了Scala和sbt
2. 在项目根目录下运行：

```bash
sbt run
```

## 使用说明

### 主菜单选项

1. **添加学生**: 添加新的学生记录
2. **删除学生**: 根据学生ID删除学生
3. **修改学生信息**: 修改学生的基本信息（姓名、邮箱等）
4. **修改学生成绩**: 修改学生的单科成绩
5. **查询学生**: 按ID或姓名查询学生
6. **显示所有学生**: 列出所有学生信息
7. **按成绩范围查询**: 查找指定科目成绩在某个范围内的学生
8. **显示统计信息**: 显示各科成绩统计
9. **保存并退出**: 保存数据到文件并退出程序

### 支持的学科

- 数学 (math)
- 历史 (history)
- 物理 (physics)
- 化学 (chemistry)
- 生物 (biology)
- 英语 (english)
- 地理 (geography)
- 平均分 (average)

### 数据格式

CSV文件包含以下字段：
- id: 学生ID
- first_name: 名
- last_name: 姓
- email: 邮箱地址
- gender: 性别
- part_time_job: 是否有兼职工作
- absence_days: 缺席天数
- extracurricular_activities: 是否参与课外活动
- weekly_self_study_hours: 每周自习时间
- career_aspiration: 职业抱负
- math_score: 数学成绩
- history_score: 历史成绩
- physics_score: 物理成绩
- chemistry_score: 化学成绩
- biology_score: 生物成绩
- english_score: 英语成绩
- geography_score: 地理成绩

## 示例操作

### 添加学生
```
添加新学生:
学生ID (留空自动生成): 
名: 张
姓: 三
邮箱: <EMAIL>
性别 (male/female): male
是否有兼职工作 (true/false): false
缺席天数: 2
是否参与课外活动 (true/false): true
每周自习时间: 20
职业抱负: Software Engineer
请输入各科成绩:
数学成绩: 85
历史成绩: 78
物理成绩: 90
化学成绩: 88
生物成绩: 82
英语成绩: 79
地理成绩: 86
```

### 查询学生
```
查询学生:
1. 按ID查询
2. 按姓名查询
请选择查询方式 (1-2): 1
请输入学生ID: 1
```

### 修改成绩
```
修改学生成绩:
请输入学生ID: 1
请输入科目 (math/history/physics/chemistry/biology/english/geography): math
请输入新成绩: 95
```

## 技术栈

- **编程语言**: Scala 2.12.17
- **构建工具**: sbt
- **数据存储**: CSV文件
- **用户界面**: 命令行界面

## 注意事项

- 程序启动时会自动加载现有的CSV数据
- 所有修改会在退出程序时自动保存
- 支持中文输入和显示
- 输入验证确保数据的完整性和正确性 