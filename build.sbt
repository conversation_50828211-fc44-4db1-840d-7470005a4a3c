ThisBuild / version := "0.1.0-SNAPSHOT"

ThisBuild / scalaVersion := "2.12.17"


lazy val root = (project in file("."))
  .settings(
    name := "SparkFinal",
    libraryDependencies ++= Seq(
      "org.apache.spark" %% "spark-core" % "3.4.1",
      "org.apache.spark" %% "spark-sql" % "3.4.1",
      "org.apache.spark" %% "spark-mllib" % "3.4.1",
      "com.crealytics" %% "spark-excel" % "3.4.1_0.20.3",
      "org.apache.poi" % "poi" % "5.2.4",
      "org.apache.poi" % "poi-ooxml" % "5.2.4"
    ),
    // JVM 配置
    fork := true,
    javaOptions ++= Seq(
      "--add-exports=java.base/sun.nio.ch=ALL-UNNAMED",
      "--add-opens=java.base/java.nio=ALL-UNNAMED",
      "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
      "--add-opens=java.base/java.lang=ALL-UNNAMED",
      "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
      "--add-opens=java.base/java.util=ALL-UNNAMED",
      "-Djava.security.manager=allow"
    )
  )
