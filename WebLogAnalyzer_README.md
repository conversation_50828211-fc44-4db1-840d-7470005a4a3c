# Web日志分析系统 - 基于Spark RDD API

## 项目概述

这是一个使用Apache Spark的RDD API开发的简化版Web日志分析系统，专门用于分析Apache服务器日志，提取关键信息并识别异常状态码。

## 功能特性

### 核心功能
1. **日志解析**: 解析Apache Combined Log Format格式的日志文件
2. **数据提取**: 提取IP地址、访问时间、URL、状态码和数据大小
3. **异常分析**: 识别并统计最常见的3个异常状态码（4xx和5xx错误）

### 技术特点
- 使用Spark RDD API实现分布式处理
- 采用函数式编程风格，提高代码可读性
- 容错处理，优雅处理解析失败的日志行
- 性能优化，使用RDD缓存机制

## 系统架构

### 数据模型
```scala
case class LogRecord(
  ip: String,        // IP地址
  timestamp: Date,   // 访问时间
  url: String,       // 请求URL
  statusCode: Int,   // HTTP状态码
  dataSize: Long     // 数据大小（字节）
)
```

### 核心组件

1. **日志解析器** (`parseLogLine`)
   - 使用正则表达式解析Apache日志格式
   - 返回`Option[LogRecord]`实现安全解析
   - 支持时间戳格式转换

2. **异常状态码分析器** (`analyzeErrorStatusCodes`)
   - 过滤4xx和5xx状态码
   - 使用RDD转换和动作操作进行统计
   - 返回按频次排序的状态码列表

3. **统计信息生成器** (`printAdditionalStats`)
   - 生成全面的日志统计信息
   - 计算异常率和唯一访问指标

## 支持的日志格式

系统解析标准的Apache Combined Log Format：
```
IP - - [时间戳] "请求方法 URL HTTP版本" 状态码 数据大小 "引用页" "用户代理"
```

示例：
```
************ - - [17/May/2015:10:05:03 +0000] "GET /presentations/logstash-monitorama-2013/images/kibana-search.png HTTP/1.1" 200 203023 "http://semicomplete.com/presentations/logstash-monitorama-2013/" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_1) AppleWebKit/537.36"
```

## 异常状态码定义

系统识别以下异常状态码：

### 4xx 客户端错误
- **400**: Bad Request (错误请求)
- **401**: Unauthorized (未授权)
- **403**: Forbidden (禁止访问)
- **404**: Not Found (页面未找到)

### 5xx 服务器错误
- **500**: Internal Server Error (服务器内部错误)
- **502**: Bad Gateway (错误网关)
- **503**: Service Unavailable (服务不可用)

## 运行方式

### 环境要求
- Scala 2.12+
- Apache Spark 3.4+
- Java 8+

### 编译运行
```bash
# 编译项目
sbt compile

# 运行分析器
sbt "runMain WebLogAnalyzerRDD"
```

### 输出示例
```
=== Web日志分析系统 - 基于RDD API ===

最常见的3个异常状态码分析结果：
--------------------------------------------------
1. 状态码 404 (Not Found (页面未找到)): 12,345 次
2. 状态码 403 (Forbidden (禁止访问)): 5,678 次  
3. 状态码 500 (Internal Server Error (服务器内部错误)): 1,234 次
--------------------------------------------------

=== 额外统计信息 ===
总日志记录数: 100,000
唯一IP地址数: 15,678
唯一URL数量: 8,901
异常请求数量: 19,257
异常率: 19.26%
```

## 代码设计亮点

### 函数式编程风格
```scala
// 使用链式操作和高阶函数
private def analyzeErrorStatusCodes(logRecords: RDD[LogRecord], topN: Int = 3): List[(Int, Long)] = {
  logRecords
    .filter(record => isErrorStatus(record.statusCode))  // 过滤异常状态码
    .map(record => (record.statusCode, 1L))              // 转换为键值对
    .reduceByKey(_ + _)                                   // 聚合计数
    .sortBy(_._2, ascending = false)                      // 降序排序
    .take(topN)                                           // 取前N个
    .toList
}
```

### 容错处理
```scala
// 使用Option类型处理解析失败
private def parseLogLine(line: String): Option[LogRecord] = {
  logPattern.findFirstMatchIn(line).flatMap { m =>
    Try {
      // 解析逻辑
      LogRecord(ip, timestamp, url, statusCode, dataSize)
    }.toOption
  }
}
```

### 性能优化
```scala
// RDD缓存提高重复访问性能
logRecords.cache()

// 使用后及时释放缓存
logRecords.unpersist()
```

## 扩展可能性

1. **更多分析维度**: 增加时间段分析、IP地址分析等
2. **实时处理**: 集成Spark Streaming处理实时日志
3. **可视化**: 添加图表和仪表板显示
4. **机器学习**: 异常检测和流量预测
5. **多格式支持**: 支持其他日志格式（如Nginx、IIS等）

## 项目结构
```
src/main/scala/
├── WebLogAnalyzerRDD.scala     # 主要分析器（RDD版本）
├── ApacheLogAnalyzer.scala     # DataFrame版本（已有）
└── ...其他文件

src/main/resources/
├── apache_logs.txt             # 测试日志文件
└── log4j2.properties          # 日志配置
```

这个系统展示了如何使用Spark RDD API以函数式编程风格处理大规模日志数据，为Web服务监控和故障排查提供有价值的洞察。 