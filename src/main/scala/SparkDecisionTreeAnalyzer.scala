import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.types._
import org.apache.spark.sql.functions._
import org.apache.spark.ml.Pipeline
import org.apache.spark.ml.classification.DecisionTreeClassifier
import org.apache.spark.ml.evaluation.MulticlassClassificationEvaluator
import org.apache.spark.ml.feature.{VectorAssembler, StringIndexer, IndexToString}
import org.apache.spark.ml.tuning.{CrossValidator, ParamGridBuilder}
import org.apache.spark.ml.param.ParamMap
import org.apache.spark.rdd.RDD
import org.apache.log4j.{Level, Logger}

/**
 * 基于Spark MLlib的决策树分析器
 * 参考Python实现，使用Spark的分布式计算能力
 */
class SparkDecisionTreeAnalyzer(@transient val spark: SparkSession) extends Serializable {
  
  import spark.implicits._
  
  // 设置日志级别
  Logger.getLogger("org").setLevel(Level.WARN)
  Logger.getLogger("akka").setLevel(Level.WARN)
  
  private var model: Option[org.apache.spark.ml.classification.DecisionTreeClassificationModel] = None
  private var pipeline: Option[org.apache.spark.ml.Pipeline] = None
  private var featureColumns: Array[String] = Array.empty
  private var targetColumn: String = ""
  
  /**
   * 数据加载器 - 从Excel文件加载数据
   */
  def loadData(filePath: String, sheetName: String = "归一化数据"): DataFrame = {
    println(s"正在加载数据文件: $filePath")
    
    // 由于Spark不直接支持Excel，我们需要先将数据转换为CSV或使用第三方库
    // 这里假设数据已经转换为CSV格式，或者使用spark-excel库
    try {
      // 如果使用spark-excel库 (需要在build.sbt中添加依赖)
      val df = spark.read
        .format("com.crealytics.spark.excel")
        .option("dataAddress", s"'$sheetName'!A1")
        .option("useHeader", "true")
        .option("treatEmptyValuesAsNulls", "true")
        .option("inferSchema", "true")
        .load(filePath)
        
      println(s"数据加载成功，行数: ${df.count()}, 列数: ${df.columns.length}")
      df.show(5)
      df
    } catch {
      case e: Exception =>
        println(s"使用Excel格式加载失败")
        // 备用方案：使用CSV格式
        spark.read
          .option("header", "true")
          .option("inferSchema", "true")
          .csv(filePath)
    }
  }
  
  /**
   * 数据预处理
   */
  def preprocessData(df: DataFrame, targetCol: String, excludeCols: Array[String] = Array("文物采样点")): DataFrame = {
    println("开始数据预处理...")
    
    // 移除指定的列
    var processedDf = excludeCols.foldLeft(df) { (tempDf, col) =>
      if (tempDf.columns.contains(col)) {
        tempDf.drop(col)
      } else {
        tempDf
      }
    }
    
    // 移除空值
    processedDf = processedDf.na.drop()
    
    // 设置目标列
    this.targetColumn = targetCol
    this.featureColumns = processedDf.columns.filter(_ != targetCol)
    
    println(s"特征列: ${featureColumns.mkString(", ")}")
    println(s"目标列: $targetColumn")
    println(s"预处理后数据行数: ${processedDf.count()}")
    
    processedDf
  }
  
  /**
   * 构建决策树模型管道
   */
  def buildPipeline(maxDepth: Int = 5, minInstancesPerNode: Int = 1, impurity: String = "entropy"): Pipeline = {
    println("构建决策树模型管道...")
    
    // 1. 将特征列组合成向量
    val assembler = new VectorAssembler()
      .setInputCols(featureColumns)
      .setOutputCol("features")
    
    // 2. 将目标列转换为索引
    val labelIndexer = new StringIndexer()
      .setInputCol(targetColumn)
      .setOutputCol("indexedLabel")
      .setHandleInvalid("keep")
    
    // 3. 创建决策树分类器
    val dt = new DecisionTreeClassifier()
      .setLabelCol("indexedLabel")
      .setFeaturesCol("features")
      .setMaxDepth(maxDepth)
      .setMinInstancesPerNode(minInstancesPerNode)
      .setImpurity(impurity) // "entropy" 对应ID3算法，"gini" 对应CART算法
      .setPredictionCol("prediction")
    
    // 4. 将预测索引转换回原始标签
    val labelConverter = new IndexToString()
      .setInputCol("prediction")
      .setOutputCol("predictedLabel")
      .setLabels(labelIndexer.fit(spark.emptyDataFrame).labelsArray(0))
    
    // 5. 创建管道
    val pipeline = new Pipeline()
      .setStages(Array(assembler, labelIndexer, dt, labelConverter))
    
    this.pipeline = Some(pipeline)
    pipeline
  }
  
  /**
   * 训练模型
   */
  def fit(df: DataFrame): Unit = {
    println("开始训练决策树模型...")
    
    require(pipeline.isDefined, "请先调用buildPipeline方法构建管道")
    
    val pipelineModel = pipeline.get.fit(df)
    
    // 提取决策树模型
    this.model = Some(pipelineModel.stages(2).asInstanceOf[org.apache.spark.ml.classification.DecisionTreeClassificationModel])
    
    println("模型训练完成!")
    
    // 打印决策树结构（类似Python代码中的tree打印）
    println("决策树结构:")
    println(model.get.toDebugString)
    
    // 打印特征重要性
    println("特征重要性:")
    val featureImportances = model.get.featureImportances.toArray
    featureColumns.zip(featureImportances).sortBy(-_._2).foreach { case (feature, importance) =>
      println(f"$feature: $importance%.4f")
    }
  }
  
  /**
   * 进行预测
   */
  def predict(df: DataFrame): DataFrame = {
    require(model.isDefined, "模型尚未训练，请先调用fit方法")
    require(pipeline.isDefined, "管道尚未构建，请先调用buildPipeline方法")
    
    val pipelineModel = pipeline.get.fit(spark.emptyDataFrame)
    pipelineModel.transform(df)
  }
  
  /**
   * 计算模型准确率
   */
  def score(df: DataFrame): Double = {
    println("计算模型准确率...")
    
    val predictions = predict(df)
    
    val evaluator = new MulticlassClassificationEvaluator()
      .setLabelCol("indexedLabel")
      .setPredictionCol("prediction")
      .setMetricName("accuracy")
    
    val accuracy = evaluator.evaluate(predictions)
    println(f"模型准确率: $accuracy%.4f")
    accuracy
  }
  
  /**
   * 交叉验证和超参数调优
   */
  def crossValidation(df: DataFrame, numFolds: Int = 3): Unit = {
    println("开始交叉验证和超参数调优...")
    
    require(pipeline.isDefined, "请先调用buildPipeline方法构建管道")
    
    // 获取决策树阶段
    val dt = pipeline.get.getStages(2).asInstanceOf[DecisionTreeClassifier]
    
    // 定义参数网格
    val paramGrid = new ParamGridBuilder()
      .addGrid(dt.maxDepth, Array(3, 5, 7, 10))
      .addGrid(dt.minInstancesPerNode, Array(1, 5, 10))
      .addGrid(dt.impurity, Array("entropy", "gini"))
      .build()
    
    // 定义评估器
    val evaluator = new MulticlassClassificationEvaluator()
      .setLabelCol("indexedLabel")
      .setPredictionCol("prediction")
      .setMetricName("accuracy")
    
    // 创建交叉验证器
    val cv = new CrossValidator()
      .setEstimator(pipeline.get)
      .setEvaluator(evaluator)
      .setEstimatorParamMaps(paramGrid)
      .setNumFolds(numFolds)
      .setParallelism(2)
    
    // 训练模型
    val cvModel = cv.fit(df)
    
    // 获取最佳模型
    val bestModel = cvModel.bestModel.asInstanceOf[org.apache.spark.ml.PipelineModel]
    val bestDT = bestModel.stages(2).asInstanceOf[org.apache.spark.ml.classification.DecisionTreeClassificationModel]
    
    this.model = Some(bestDT)
    
    println("交叉验证完成!")
    println(s"最佳模型参数:")
    println(s"  最大深度: ${bestDT.getMaxDepth}")
    println(s"  最小实例数: ${bestDT.getMinInstancesPerNode}")
    println(s"  不纯度度量: ${bestDT.getImpurity}")
    
    // 打印最佳模型结构
    println("最佳决策树结构:")
    println(bestDT.toDebugString)
  }
  
  /**
   * 计算熵（用于分析，对应Python代码中的entropy方法）
   */
  def calculateEntropy(df: DataFrame, column: String): Double = {
    val counts = df.groupBy(column).count().collect()
    val total = counts.map(_.getAs[Long]("count")).sum.toDouble
    
    val entropy = counts.map { row =>
      val count = row.getAs[Long]("count").toDouble
      val probability = count / total
      if (probability > 0) -probability * math.log(probability) / math.log(2) else 0.0
    }.sum
    
    entropy
  }
  
  /**
   * 计算信息增益
   */
  def calculateInformationGain(df: DataFrame, attribute: String, target: String): Double = {
    val totalEntropy = calculateEntropy(df, target)
    val total = df.count().toDouble
    
    val weightedEntropy = df.groupBy(attribute).agg(count("*").as("count"))
      .collect()
      .map { row =>
        val value = row.get(0)
        val count = row.getAs[Long]("count").toDouble
        val subset = df.filter(col(attribute) === value)
        val subsetEntropy = calculateEntropy(subset, target)
        (count / total) * subsetEntropy
      }.sum
    
    totalEntropy - weightedEntropy
  }
}

/**
 * 主程序对象
 */
object SparkDecisionTreeAnalyzer {
  
  def main(args: Array[String]): Unit = {
    // 创建Spark会话
    val spark = SparkSession.builder()
      .appName("Spark决策树分析器")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .getOrCreate()
    
    try {
      // 创建分析器实例
      val analyzer = new SparkDecisionTreeAnalyzer(spark)
      
      // 数据文件路径
      val dataPath = "src/main/resources/附件1处理后的数据.xlsx"
      
      // 加载数据
      val rawData = analyzer.loadData(dataPath, "归一化数据")
      
      // 数据预处理
      val processedData = analyzer.preprocessData(rawData, "有无风化", Array("文物采样点"))
      
      // 数据分割（训练集和测试集）
      val Array(trainData, testData) = processedData.randomSplit(Array(0.8, 0.2), seed = 42)
      
      println(s"训练集大小: ${trainData.count()}")
      println(s"测试集大小: ${testData.count()}")
      
      // 构建管道并训练模型
      analyzer.buildPipeline(maxDepth = 5, minInstancesPerNode = 1, impurity = "entropy")
      analyzer.fit(trainData)
      
      // 在测试集上评估模型
      val testAccuracy = analyzer.score(testData)
      
      // 进行交叉验证（可选）
      println("\n=== 开始交叉验证 ===")
      analyzer.crossValidation(trainData, numFolds = 3)
      
      // 最终评估
      val finalAccuracy = analyzer.score(testData)
      println(f"\n最终模型在测试集上的准确率: $finalAccuracy%.4f")
      
      // 计算一些示例的信息增益（展示额外分析功能）
      println("\n=== 信息增益分析 ===")
      val featureColumns = processedData.columns.filter(_ != "有无风化")
      featureColumns.take(3).foreach { feature =>
        val gain = analyzer.calculateInformationGain(processedData, feature, "有无风化")
        println(f"特征 '$feature' 的信息增益: $gain%.4f")
      }
      
    } catch {
      case e: Exception =>
        println(s"程序执行出错: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
} 