import org.apache.spark.sql.SparkSession
import org.apache.log4j.{Level, Logger}

object DataChecker {
  def main(args: Array[String]): Unit = {
    // 设置日志级别
    Logger.getLogger("org").setLevel(Level.WARN)
    Logger.getLogger("akka").setLevel(Level.WARN)
    
    val spark = SparkSession.builder()
      .appName("数据检查器")
      .master("local[*]")
      .getOrCreate()
      
    try {
      println("=== 尝试加载Excel文件 ===")
      val dataPath = "src/main/resources/附件1处理后的数据.xlsx"
      
      // 方法1：尝试使用spark-excel
      try {
        val df = spark.read
          .format("com.crealytics.spark.excel")
          .option("dataAddress", "'归一化数据'!A1")
          .option("useHeader", "true")
          .option("treatEmptyValuesAsNulls", "true")
          .option("inferSchema", "true")
          .load(dataPath)
          
        println(s"Excel加载成功！")
        println(s"行数: ${df.count()}")
        println(s"列数: ${df.columns.length}")
        println("列名:")
        df.columns.foreach(col => println(s"  - $col"))
        println("数据样例:")
        df.show(3, false)
        
      } catch {
        case e: Exception =>
          println(s"Excel加载失败: ${e.getMessage}")
          
          // 方法2：如果有CSV版本，尝试加载CSV
          val csvPath = "src/main/resources/附件1处理后的数据.csv"
          try {
            val csvDf = spark.read
              .option("header", "true")
              .option("inferSchema", "true")
              .csv(csvPath)
            println("CSV加载成功!")
            println(s"行数: ${csvDf.count()}")
            println(s"列数: ${csvDf.columns.length}")
            println("列名:")
            csvDf.columns.foreach(col => println(s"  - $col"))
            csvDf.show(3, false)
          } catch {
            case csvE: Exception =>
              println(s"CSV加载也失败: ${csvE.getMessage}")
              println("请确保数据文件存在且格式正确")
          }
      }
    } finally {
      spark.stop()
    }
  }
} 